@import '/variable.less';

.parking-list-container {
  min-height: 100vh;
  background-color: #F7F8FA;
  position: relative;

  .parking-list-content {
    padding: 24rpx 24rpx 120rpx;
    position: relative;
    z-index: 1;
    margin-top: 30rpx; /* 增加顶部边距，与浮动头部保持距离 */
  }
}

/* 浮动头部样式 */
.header-main {
  text-align: center;
  padding: 20rpx 30rpx;
  position: relative;
  z-index: 2;
}

.parking-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  text-align: center;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.header-secondary {
  padding: 0 30rpx 20rpx;
}

.parking-info-section {
  margin: 10rpx auto 20rpx;
  text-align: center;
  position: relative;
  z-index: 2;
}

.info-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.15);
  color: #fff;
  font-size: 26rpx;
  padding: 8rpx 20rpx;
  border-radius: 24rpx;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  max-width: 90%;
  position: relative;

  t-icon {
    margin-right: 8rpx;
    flex-shrink: 0;
  }

  text {
    font-weight: 400;
    word-break: keep-all; // 保持单词完整，避免中文词汇被截断
    word-wrap: break-word; // 优先在单词边界换行
    overflow-wrap: break-word; // 现代浏览器的换行属性
    flex: 1;
    line-height: 1.3; // 设置行高，改善多行文字的可读性

    // 限制最多显示两行，超出部分省略
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .navigation-icon {
    margin-left: 12rpx;
    // padding: 8rpx;
    // border-radius: 50%;
    // background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }

    &:active {
      background-color: rgba(255, 255, 255, 0.3);
      transform: scale(0.95);
    }

    t-icon {
      margin: 0;
    }
  }
}

.parking-meta-section {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-top: 16rpx;
}

.meta-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.15);
  color: #fff;
  font-size: 26rpx;
  padding: 8rpx 20rpx;
  border-radius: 24rpx;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);

  t-icon {
    margin-right: 8rpx;
  }

  text {
    font-weight: 400;
  }
}

/* 停车场信息 */
.parking-info {
  display: flex;
  background-color: @bg-color-white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
  border-left: 6rpx solid @brand7-normal;
  border-bottom: 1rpx solid #f0f0f0;
}

.parking-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
  background-color: #f5f5f5;

  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.parking-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.parking-name {
  font-size: 36rpx;
  font-weight: 500;
  color: @gy1;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.parking-address {
  font-size: 26rpx;
  color: #999999;
  margin-bottom: 16rpx;
  line-height: 1.4;
  display: flex;
  align-items: center;
}

.parking-meta {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  flex-wrap: wrap;
}

.parking-distance {
  color: #999999;
  margin-right: 24rpx;
}

.parking-spots {
  color: @gy1;
  margin-right: 24rpx;
  display: flex;
  align-items: center;
}

.parking-price {
  color: @gy1;
  display: flex;
  align-items: center;
}

/* 温馨提示 */
.notice-card {
  background-color: #FFF7E6;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.notice-header {
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;

  t-icon {
    margin-right: 8rpx;
  }
}

.notice-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #E37318;
}

.notice-content {
  padding: 4rpx 0;
}

.notice-item {
  margin-bottom: 12rpx;
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  position: relative;
  padding-left: 20rpx;

  &::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #E37318;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

/* 车位列表 */
.spots-list {
  margin-top: 20rpx;
}

.spot-item {
  display: flex;
  background-color: @bg-color-white;
  border-radius: 30rpx; /* 更大的圆角，与parking-detail保持一致 */
  padding: 24rpx;
  margin-bottom: 30rpx; /* 增加间距 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
  transition: transform 0.2s ease;
  will-change: transform; /* 提示浏览器这个元素会变化，优化渲染性能 */
  transform: translateZ(0); /* 启用GPU加速 */

  &:active {
    transform: scale(0.98);
  }

  &.spot-busy {
    opacity: 0.95;
  }

  &.spot-unavailable {
    opacity: 0.8;
    background-color: #F9F9F9;

    &:active {
      transform: none;
    }
  }
}

.spot-left {
  margin-right: 20rpx;
}

.spot-image-placeholder {
  width: 120rpx; /* 增加尺寸 */
  height: 120rpx; /* 增加尺寸 */
  border-radius: 20rpx; /* 更大的圆角 */
  background-color: rgba(0, 82, 217, 0.05); /* 使用主题色的浅色背景 */
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.spot-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.spot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.spot-name {
  font-size: 36rpx; /* 增大字体 */
  font-weight: 500;
  color: @gy1;
  line-height: 1.2;
}

.status-tags {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.spot-location {
  font-size: 26rpx;
  color: #959595; /* 统一颜色 */
  line-height: 1.4;
  display: flex;
  align-items: flex-start; // 改为顶部对齐，让图标与第一行文字对齐
  margin-bottom: 8rpx;

  t-icon {
    margin-right: 8rpx;
    margin-top: 2rpx; // 微调图标位置，与文字基线更好对齐
    flex-shrink: 0;
  }

  text {
    word-break: keep-all; // 保持单词完整，避免中文词汇被截断
    word-wrap: break-word; // 优先在单词边界换行
    overflow-wrap: break-word; // 现代浏览器的换行属性
    flex: 1; // 让文字占据剩余空间

    // 限制最多显示两行，超出部分省略
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.spot-time {
  font-size: 26rpx;
  color: #959595; /* 统一颜色 */
  line-height: 1.4;
  display: flex;
  align-items: center;
  margin-bottom: 16rpx; /* 增加间距 */

  t-icon {
    margin-right: 8rpx;
    flex-shrink: 0;
  }

  .cross-day-tag {
    margin-left: 8rpx;
    font-size: 22rpx;
    color: #fff;
    background-color: @brand7-normal; /* 使用主题色 */
    padding: 2rpx 10rpx;
    border-radius: 4rpx;
  }
}

/* 添加分隔线 */
.divider {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 12rpx 0;
}

.spot-price {
  font-size: 30rpx;
  color: @gy1; /* 使用统一的文本颜色 */
  font-weight: 500;
  margin-top: 8rpx;

  .price-unit {
    font-size: 24rpx;
    color: #959595; /* 统一颜色 */
    font-weight: 400;
    margin-left: 2rpx;
  }
}

.spot-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;

  t-icon {
    padding: 16rpx; /* 增加点击区域 */
    border-radius: 50%;
    transition: all 0.2s ease;
    background-color: rgba(0, 82, 217, 0.05); /* 添加背景色 */

    &:active {
      background-color: rgba(0, 82, 217, 0.1); /* 点击时加深背景色 */
      transform: scale(0.95);
    }
  }
}

.status-tag {
  padding: 4rpx 12rpx;
  border-radius: 20rpx; /* 更圆润的边角 */
  font-size: 22rpx;
  text-align: center;
  white-space: nowrap;

  &.status-free {
    color: @bg-color-white;
    background-color: @brand-contrast-green; /* 使用对比绿色 */
  }

  &.status-busy {
    color: @bg-color-white;
    background-color: @brand7-normal; /* 使用主题蓝色 */
  }

  &.status-unavailable {
    color: @bg-color-white;
    background-color: #FA9D3B; /* 使用橙色代替红色，更协调 */
  }
}

/* 加载和空状态 */
.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background-color: @bg-color-white;
  border-radius: 30rpx; /* 更大的圆角，与卡片保持一致 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx; /* 增加间距 */
}

.loading-text {
  margin-top: 16rpx;
  font-size: 26rpx;
  color: #959595; /* 统一颜色 */
}

/* 下拉刷新样式优化 */
.t-pull-down-refresh {
  &__track {
    background-color: transparent;
  }

  &__text {
    color: #959595; /* 统一颜色 */
    font-size: 26rpx;
  }
}

/* 加载更多样式 */
.load-more-wrapper {
  padding: 24rpx 0 40rpx;
  text-align: center;

  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 0;

    .loading-text {
      margin-left: 12rpx;
      color: #959595;
      font-size: 28rpx;
    }
  }

  .load-more-btn {
    display: inline-block;
    padding: 16rpx 32rpx;
    color: @brand7-normal;
    font-size: 28rpx;
    background-color: rgba(0, 82, 217, 0.05);
    border-radius: 32rpx;

    &:active {
      opacity: 0.8;
    }
  }

  .no-more {
    padding: 20rpx 0;
    color: #959595;
    font-size: 28rpx;
  }
}
