import config from '~/config';

// const { baseUrl } = config;
// const baseUrl = 'https://dev-park.ymqct.com'
const baseUrl = 'https://share.ymqct.com'
const delay = config.isMock ? 0 : 0;
/**
 * 统一请求函数
 * @param {string} url - 请求路径
 * @param {string} method - 请求方法
 * @param {Object} data - 请求数据
 * @returns {Promise} - 返回Promise对象
 */
function request(url, method = 'GET', data = {}, ckAuth = true) {
  const header = {
    'content-type': 'application/json',
    // 有其他content-type需求加点逻辑判断处理即可
  };

  // 获取token，有就丢进请求头
  const tokenString = wx.getStorageSync('access_token');
  if (tokenString != '') {
    header.token = `${tokenString}`;
  }

  return new Promise((resolve, reject) => {
    wx.request({
      url: baseUrl + url,
      method,
      data,
      dataType: 'json', // 微信官方文档中介绍会对数据进行一次JSON.parse
      header,
      success(res) {
        setTimeout(() => {
          // 处理HTTP状态码
          if (res.statusCode === 200) {
            // 正常返回数据
            resolve(res);
          } else if (res.statusCode === 401 && ckAuth) {
            console.log('收到401状态码:', res.data);
            // 清除token
            wx.removeStorageSync('access_token');
            // 设置标记，确保用户进入首页时会看到登录弹窗
            wx.setStorageSync('show_login_overlay', true);
            // wx.setStorageSync('login_error_message', res.data?.msg || '登录已过期，请重新登录');

            console.log('已设置 show_login_overlay = true');

            // 获取当前页面路径
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1];
            const currentRoute = currentPage ? currentPage.route : '';

            // 如果当前已经在首页，则不需要跳转，只需要刷新页面
            if (currentRoute === 'pages/home-park/index') {
              console.log('当前已在首页，触发页面刷新');
              // 触发页面的 onShow 方法来刷新登录状态
              if (currentPage && typeof currentPage.onShow === 'function') {
                currentPage.onShow();
              }
            } else {
              // 使用switchTab跳转到首页，这比navigateBack更可靠
              console.log('跳转到首页');
              wx.switchTab({
                url: '/pages/home-park/index',
                fail: (err) => {
                  console.error('跳转到首页失败:', err);
                  // 如果switchTab失败，尝试使用reLaunch
                  wx.reLaunch({
                    url: '/pages/home-park/index'
                  });
                }
              });
            }

            // 拒绝请求，让调用方知道请求失败
            reject({
              errMsg: '登录已过期，请重新登录',
              statusCode: 401,
              data: res.data
            });
          } else {
            // 修复这里的错误，使用res而不是未定义的err
            reject({
              errMsg: '请求失败',
              statusCode: res.statusCode,
              data: res.data
            });
          }

        }, delay);
      },
      fail(err) {
        setTimeout(() => {
          // 断网、服务器挂了都会fail回调，直接reject即可
          reject(err);
        }, delay);
      },
    });
  });
}

// 导出请求函数
export default request;
