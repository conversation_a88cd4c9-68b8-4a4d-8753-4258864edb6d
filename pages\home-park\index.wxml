
<view class="home-park-container">
  <!-- <view class="nav-placeholder"></view> -->
  <view class="home-park-content">
    <nav nav-type="search" background="{{false}}" />
    <!-- 添加简化的装饰元素 -->
    <!-- 圆形装饰 - 品牌蓝色系 -->
    <view class="decoration-circle-large"></view>
    <view class="decoration-circle-medium"></view>

    <!-- 圆形装饰 - 绿色系 -->
    <view class="decoration-circle-small"></view>
    <!-- 轮播图 -->
    <view class="banner-container">
      <t-swiper
        list="{{swiperList}}"
        navigation="{{ { type: 'dots-bar' } }}"
        autoplay="{{true}}"
        interval="{{3000}}"
        height="300rpx"
        t-class="t-swiper"
      />
    </view>

    <!-- 快捷操作区 -->
    <view class="quick-actions-container">
      <t-row gutter="16">
        <t-col span="12" t-class="quick-actions-left">
          <home-card
            tags="{{[]}}"
            height="316rpx"
            url="https://share-park.oss-cn-hangzhou.aliyuncs.com//card/card-01.jpg"
            bind:tap="navigateToPage"
            data-navto="/pages/parking-list/index"
            data-noauth="{{true}}"
          />
        </t-col>
        <t-col span="12" t-class="quick-actions-right">
          <home-card
            url="https://share-park.oss-cn-hangzhou.aliyuncs.com//card/card-02.jpg"
            tags="{{[]}}"
            desc="{{lockHasOrderDesc}}"
            height="150rpx"
            bind:tap="navigateToPage"
            data-navto="/pages/parking-management/index"
          />
          <home-card
            url="https://share-park.oss-cn-hangzhou.aliyuncs.com//card/card-03.jpg"
            tags="{{[]}}"
            desc="{{usingOrdersDesc}}"
            height="150rpx"
            bind:tap="navigateToPage"
            data-navto="/pages/parking-bill/index"
          />
        </t-col>
      </t-row>
    </view>

    <!-- 附近车场 -->
    <view class="section-title">
      <text class="title-text">附近车场</text>
      <view class="more-text">
        <view class="relocate-button" bind:tap="refNearlyPark">
          <t-icon name="map-aiming" size="28rpx" />
          <text>刷新列表</text>
        </view>
      </view>
    </view>
    <view class="nearby-parking-list">
      <!-- 骨架屏 -->
      <view wx:if="{{nearbyParkingLoading}}" class="nearby-parking-skeleton">
        <view class="nearby-parking-skeleton-item" wx:for="{{[1,2,3]}}" wx:key="*this">
          <t-skeleton
            loading="{{true}}"
            animation="gradient"
            row-col="{{[1, 1, 1]}}"
            t-class="nearby-parking-skeleton-content"
          />
        </view>
      </view>

      <!-- 实际内容 -->
      <view wx:else>
        <view class="nearby-parking-item" wx:for="{{nearbyParkingList}}" wx:key="id" bind:tap="onParkingItemTap" data-id="{{item.id}}">
          <view class="nearby-parking-info">
            <view class="nearby-parking-name-row">
              <view class="nearby-parking-name">{{item.parkName}}</view>
              <view class="nearby-parking-distance">{{item.distance}}</view>
            </view>
            <view class="nearby-parking-address">
              <t-icon name="location" size="26rpx" />
              <text>{{item.parkAddress}}</text>
            </view>
            <view class="nearby-parking-details">
              <view class="nearby-parking-spaces">
                <text class="spaces-label">剩余车位: </text>
                <text class="spaces-value">{{item.availableSlots}}</text>
              </view>
              <view class="nearby-parking-price">
                <text class="price-value">¥{{item.averagePrice}}</text>
                <text class="price-unit">/小时</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 底部广告区域 -->
    <view class="adv-container">
      <image class="adv-image" src="https://share-park.oss-cn-hangzhou.aliyuncs.com/adv/adv-01.jpg" mode="widthFix" bind:tap="contactCustomerService"/>
    </view>
  </view>
  <!-- 悬浮分享按钮 -->
  <t-fab
    icon="share"
    text="分享"
    t-class="floating-share-button"
    button-props="{{shareBtnCig}}"
  />

</view>

<t-message offset="{{['110rpx', '18rpx']}}" id="t-message" />



<!-- 分享成功提示弹窗 -->
<t-popup
  visible="{{shareSuccessVisible}}"
  placement="center"
  bind:visible-change="onShareSuccessVisibleChange"
  custom-style="background: transparent;"
>
  <view class="share-success-content">
    <view class="share-success-icon">
      <t-icon name="check-circle-filled" size="80rpx" color="#00A870" />
    </view>
    <view class="share-success-text">分享成功</view>
  </view>
</t-popup>

<!-- Add the login overlay component at the end of the page -->
<login-overlay
  visible="{{loginOverlayVisible}}"
  bind:overlayclick="onLoginOverlayClick"
  bindcustomevent="handleLoginSuccess"
/>

