@import '/variable.less';

.home-park-container {
  height: calc(100vh - @tab-bar-height);
  // background-color: #F7F8FA;
  // position: relative;
  // overflow: hidden; // 确保装饰元素不会溢出容器
  // padding-top: @navbar-padding-top;
  // 导航栏占位元素
  // .nav-placeholder {
  //   height: @nav-bar-height; // 与导航栏高度一致
  //   width: 100%;
  // }

  // 添加装饰元素动画 - 简化版本
  @keyframes pulse {
    0% {
      opacity: 0.7;
    }
    50% {
      opacity: 0.9;
    }
    100% {
      opacity: 0.7;
    }
  }

  @keyframes rotateSquare {
    0% {
      transform: rotate(45deg);
    }
    50% {
      transform: rotate(55deg);
    }
    100% {
      transform: rotate(45deg);
    }
  }

  // 添加顶部大型装饰圆形 - 使用品牌蓝色
  .decoration-circle-large {
    position: absolute;
    width: 1000rpx;
    height: 1000rpx;
    border-radius: 50%;
    background-color: rgba(0, 82, 217, 0.14); // 品牌蓝色 #0052D9
    top: -400rpx;
    right: -300rpx;
    z-index: 0;
    pointer-events: none; // 确保不会影响点击事件
  }

  // 添加顶部中型装饰圆形 - 使用品牌蓝色
  .decoration-circle-medium {
    position: absolute;
    width: 800rpx;
    height: 800rpx;
    border-radius: 50%;
    background-color: rgba(0, 82, 217, 0.12); // 品牌蓝色 #0052D9
    top: -200rpx;
    left: -300rpx;
    z-index: 0;
    animation: pulse 8s ease-in-out infinite;
    pointer-events: none; // 确保不会影响点击事件
  }

  // 添加中部小型装饰圆形 - 使用绿色
  .decoration-circle-small {
    position: absolute;
    width: 600rpx;
    height: 600rpx;
    border-radius: 50%;
    background-color: rgba(7, 193, 96, 0.12); // 绿色 #07C160
    top: 400rpx;
    left: -200rpx;
    z-index: 0;
    animation: pulse 12s ease-in-out infinite;
    pointer-events: none; // 确保不会影响点击事件
  }

  .home-park-content {
    height: calc(100% );
    overflow-y: auto;
    overflow-x: hidden;
    // padding-top: 5rpx; // 增加顶部内边距，避免内容被装饰元素遮挡
    // padding-top: @nav-bar-height;
    padding-bottom: calc(32rpx + env(safe-area-inset-bottom)); // 增加底部内边距，适配底部安全区域
    position: relative;
    box-sizing: border-box; // 确保内边距不会增加元素的总高度

    // 添加顶部渐变背景 - 使用品牌蓝色和绿色
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 900rpx; // 增加高度，延伸至屏幕中间
      background: linear-gradient(135deg,
        rgba(0, 82, 217, 0.1) 0%, // 品牌蓝色 #0052D9
        rgba(7, 193, 96, 0.05) 80%, // 绿色 #07C160
        rgba(7, 193, 96, 0) 100% // 绿色 #07C160
      );
      z-index: 0;
      pointer-events: none; // 确保不会影响点击事件
    }
  }
}
.light {
  font-size: 28rpx;
  line-height: 96rpx;
  text-align: center;
}

/* 搜索框样式 */
.search-container {
  padding: 24rpx 32rpx;
  background-color: @bg-color-white;
}

.search-input {
  border-radius: 32rpx;
}

.t-search__input-container {
  border-radius: 32rpx !important;
  height: 64rpx !important;
  background-color: #F5F7FA;
}

/* 轮播图样式 */
.banner-container {
  margin: 0 32rpx;
  margin-top: 24rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #e0e0e0; /* 占位背景色 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 1; // 确保在装饰元素之上
}

.t-swiper {
  border-radius: 12rpx;

  // 自定义导航点样式
  :deep(.t-swiper__navigation-dots-bar) {
    --td-swiper-nav-dot-active-color: @brand7-normal;
    --td-swiper-nav-dot-color: rgba(255, 255, 255, 0.5);
  }
}

/* 快捷操作区样式 */
.quick-actions-container {
  margin: 24rpx 32rpx;
  position: relative;
  z-index: 1; // 确保在装饰元素之上
}

.quick-actions-left {
  // 左侧列样式
  .home-card {
    margin: 0; // 移除默认 margin，使用 gutter 控制间距
  }
}

.quick-actions-right {
  // 右侧列样式
  display: flex !important;
  flex-direction: column;
  justify-content: space-between;
  .home-card {
    margin: 0; // 移除默认 margin，使用 gutter 控制间距
  }
}

.grid-container {
  padding: 16rpx 0;
  position: relative;
  z-index: 1; // 确保内容在渐变背景之上
}

.grid-item {
  padding: 16rpx 0;
}

.grid-text {
  font-size: 28rpx;
  color: @gy1;
  margin-top: 8rpx;
}

.grid-image {
  width: 80rpx;
  height: 80rpx;
  color: @brand7-normal;
}

/* 标题样式 */
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx 16rpx; // 减少上下内边距，使其更紧凑
  position: relative;
  z-index: 1; // 确保在装饰元素之上

  .title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: @gy1;
  }

  .more-text {
    font-size: 28rpx;
    color: @brand7-normal;
    display: flex;
    align-items: center;

    .relocate-button {
      display: flex;
      align-items: center;
      padding: 8rpx 16rpx;
      background-color: rgba(0, 82, 217, 0.08);
      border-radius: 24rpx;
      color: @brand7-normal;
      font-size: 26rpx;
      transition: all 0.2s ease;
      border: 1rpx solid rgba(0, 82, 217, 0.1);
      box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
      transition: transform 0.2s ease;
      will-change: transform; /* 提示浏览器这个元素会变化，优化渲染性能 */
      transform: translateZ(0); /* 启用GPU加速 */

      &:active {
        transform: scale(0.98);
      }
      t-icon {
        margin-right: 6rpx;
      }

      &:active {
        transform: translateY(2rpx);
        background-color: rgba(0, 82, 217, 0.12);
        box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
      }
    }
  }
}
// 快捷卡片
.acccard-img-01 {
  background-image: "https://share-park.oss-cn-hangzhou.aliyuncs.com//card-01.jpg";
}
/* 推荐车场列表样式 */
.parking-list {
  padding: 0 32rpx;
}

.parking-item {
  display: flex;
  margin-bottom: 24rpx;
  padding: 24rpx;
  background-color: @bg-color-white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.parking-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  background-color: #e0e0e0; /* 占位背景色 */
}

.parking-info {
  flex: 1;
  margin-left: 24rpx;
  display: flex;
  flex-direction: column;
}

.parking-name {
  font-size: 32rpx;
  font-weight: 600;
  color: @gy1;
  margin-bottom: 12rpx;
}

.parking-address {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: @gy2;
  margin-bottom: 12rpx;

  text {
    margin-left: 8rpx;
  }
}

.parking-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.parking-distance {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: @gy2;

  text {
    margin-left: 8rpx;
  }
}

.parking-price {
  font-size: 28rpx;

  .price-value {
    color: #ff4d4f;
    font-weight: 600;
  }

  .price-unit {
    color: @gy3;
  }
}

.parking-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

/* 热门车场列表样式 */
.hot-parking-scroll {
  width: 100%;
}

.hot-parking-list {
  display: flex;
  padding: 0 32rpx;
  padding-right: 0;
}

.hot-parking-item {
  flex: 0 0 auto;
  width: 280rpx;
  margin-right: 24rpx;
  background-color: @bg-color-white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.hot-parking-image {
  width: 280rpx;
  height: 200rpx;
  background-color: #e0e0e0; /* 占位背景色 */
}

.hot-parking-info {
  padding: 16rpx;
}

.hot-parking-name {
  font-size: 28rpx;
  font-weight: 600;
  color: @gy1;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.hot-parking-details {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
}

.hot-parking-distance {
  color: @gy2;
}

.hot-parking-price {
  color: @brand-contrast-green;
}

/* 附近车场列表样式 */
.nearby-parking-list {
  padding: 0 32rpx;
  margin-top: -8rpx; // 添加负边距，使其与标题更紧凑
  position: relative;
  z-index: 1; // 确保在装饰元素之上
}

/* 骨架屏样式 */
.nearby-parking-skeleton {
  .nearby-parking-skeleton-item {
    margin-bottom: 20rpx;
    padding: 20rpx 24rpx;
    background-color: @bg-color-white;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;

    // 添加微妙的渐变背景，与实际列表项保持一致
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(to bottom right, rgba(255, 255, 255, 1), rgba(248, 249, 250, 0.5));
      z-index: 0;
    }

    // 添加左侧微妙的装饰线，与实际列表项保持一致
    &::after {
      content: '';
      position: absolute;
      top: 12rpx;
      bottom: 12rpx;
      left: 0;
      width: 6rpx;
      background-color: rgba(0, 82, 217, 0.05);
      border-radius: 3rpx;
      z-index: 0;
    }

    .nearby-parking-skeleton-content {
      position: relative;
      z-index: 1; // 确保骨架屏内容在渐变背景之上
    }
  }
}

.nearby-parking-item {
  margin-bottom: 20rpx; // 减少底部边距
  padding: 20rpx 24rpx; // 减少内边距
  background-color: @bg-color-white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  transition: transform 0.2s ease;
  will-change: transform; /* 提示浏览器这个元素会变化，优化渲染性能 */
  transform: translateZ(0); /* 启用GPU加速 */

  &:active {
    transform: scale(0.98);
  }
  // 添加微妙的渐变背景
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom right, rgba(255, 255, 255, 1), rgba(248, 249, 250, 0.5));
    z-index: 0;
  }

  // 添加左侧微妙的装饰线
  &::after {
    content: '';
    position: absolute;
    top: 12rpx;
    bottom: 12rpx;
    left: 0;
    width: 6rpx;
    background-color: rgba(0, 82, 217, 0.05);
    border-radius: 3rpx;
    z-index: 0;
  }
}

.nearby-parking-info {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1; // 确保内容在渐变背景之上
}

.nearby-parking-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx; // 减少底部边距
}

.nearby-parking-name {
  font-size: 30rpx; // 稍微减小字体大小
  font-weight: 600;
  color: @gy1;
}

.nearby-parking-distance {
  font-size: 26rpx; // 减小字体大小
  color: @gy2;
}

.nearby-parking-address {
  display: flex;
  align-items: flex-start; // 改为顶部对齐，让图标与第一行文字对齐
  font-size: 26rpx; // 减小字体大小
  color: @gy2;
  margin-bottom: 12rpx; // 减少底部边距

  t-icon {
    color: @gy2;
    margin-top: 2rpx; // 微调图标位置，与文字基线更好对齐
    flex-shrink: 0; // 防止图标被压缩
  }

  text {
    margin-left: 8rpx;
    line-height: 1.4; // 设置行高，改善多行文字的可读性
    word-break: keep-all; // 保持单词完整，避免中文词汇被截断
    word-wrap: break-word; // 优先在单词边界换行
    overflow-wrap: break-word; // 现代浏览器的换行属性
    flex: 1; // 让文字占据剩余空间
    max-width: calc(100% - 34rpx); // 限制最大宽度，为图标留出空间
  }
}

.nearby-parking-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4rpx; // 添加顶部边距
}

.nearby-parking-spaces {
  font-size: 26rpx; // 减小字体大小

  .spaces-label {
    color: @gy2;
  }

  .spaces-value {
    color: @brand7-normal;
    font-weight: 600;
  }
}

.nearby-parking-price {
  font-size: 26rpx; // 减小字体大小

  .price-value {
    color: @brand7-light;
    font-weight: 600;
  }

  .price-unit {
    color: @gy3;
  }
}

/* 底部广告区域样式 */
.adv-container {
  margin: 32rpx;
  margin-bottom: calc(32rpx + @tab-bar-height); // 增加底部间距，避免被tabbar遮挡
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 1;
}

.adv-image {
  width: 100%;
  display: block;
  border-radius: 12rpx;
}
