<t-toast id="t-toast" />

<view class="my-container">
  <!-- 顶部背景 -->
  <view class="my-header">
    <view class="decoration-circle top-right"></view>
    <view class="decoration-circle bottom-left"></view>
    <view class="decoration-circle bottom-right"></view>

    <!-- 用户信息区域 -->
    <view class="user-info">
      <view class="avatar-container">
        <image class="avatar" src="{{isLoad ? personalInfo.image : '/static/my/avatar.png'}}" />
        <view wx:if="{{isLoad}}" class="level-tag">LV {{personalInfo.level || 1}}</view>
      </view>
      <view class="user-details">
        <view class="username">{{isLoad ? personalInfo.name : '共享车位用户'}}</view>
        <view wx:if="{{!isLoad}}" class="login-btn" bindtap="onEleClick" data-data="{{loginData}}">点击登录/注册</view>
      </view>
      <view wx:if="{{isLoad}}" class="edit-icon" bindtap="loginOut">
        <t-icon name="logout" size="40rpx" color="#FFFFFF" />
        切换账号
      </view>
    </view>

    <!-- 账户信息 -->
    <view class="account-info">
      <view class="account-item" bindtap="onEleClick" data-data="{{walletData}}" >
        <view class="account-value">{{isLoad ? personalInfo.balance || '0.00' : '0.00'}}元</view>
        <view class="account-label">钱包余额</view>
      </view>
      <view class="account-divider"></view>
      <view class="account-item" bindtap="onEleClick" data-data="{{spotsMana}}">
        <view class="account-value">{{isLoad ? personalInfo.spots || '0' : '0'}}</view>
        <view class="account-label">车位管理</view>
      </view>
      <view class="account-divider" ></view>
      <view class="account-item" bindtap="onEleClick" data-data="{{plate}}">
        <view class="account-value">{{isLoad ? personalInfo.cars || '0' : '0'}}</view>
        <view class="account-label">车牌管理</view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="my-content">
    <!-- 服务卡片 -->
    <view class="card service-card">
      <view class="card-item" bindtap="onEleClick" data-data="{{incomeData}}">
          <view class="card-text">车位收入</view>
        <view class="card-arrow">
          <t-icon name="chevron-right" size="36rpx" color="#CCCCCC" />
        </view>
      </view>
      <view class="card-divider"></view>
      <view class="card-item" bindtap="onEleClick" data-data="{{billData}}">
          <view class="card-text">全部账单</view>
        <view class="card-arrow">
          <t-icon name="chevron-right" size="36rpx" color="#CCCCCC" />
        </view>
      </view>
      <!-- <view class="card-divider"></view> -->
      <!-- <view class="card-item" bindtap="onEleClick" data-data="{{walletData}}">
        <view class="card-text">账户钱包</view>
        <view class="card-arrow">
          <t-icon name="chevron-right" size="36rpx" color="#CCCCCC" />
        </view>
      </view>
      <view class="card-divider"></view>
      <view class="card-item" bindtap="onEleClick" data-data="{{carData}}">
        <view class="card-text">车辆管理</view>
        <view class="card-arrow">
          <t-icon name="chevron-right" size="36rpx" color="#CCCCCC" />
        </view>
      </view> -->
    </view>

    <!-- 客服和反馈 -->
    <view class="card support-card">
      <view class="card-item" bindtap="onEleClick" data-data="{{serviceData}}">
        <view class="card-text">联系客服</view>
        <view class="card-arrow">
          <t-icon class="card-icon" name="chevron-right" size="36rpx" color="#CCCCCC" />
        </view>
      </view>
      <view class="card-divider"></view>
      <view class="card-item" bindtap="onEleClick" data-data="{{feedbackData}}">
        <view class="card-text">投诉建议</view>
        <view class="card-arrow">
          <t-icon name="chevron-right" size="36rpx" color="#CCCCCC" />
        </view>
      </view>
    </view>
  </view>
</view>
<!-- Add the login overlay component at the end of the page -->
<login-overlay
  visible="{{loginOverlayVisible}}"
  bind:overlayclick="onLoginOverlayClick"
  bindcustomevent="handleLoginSuccess"
/>

<!-- 钱包余额弹窗 -->
<t-popup
  visible="{{walletPopupVisible}}"
  placement="center"
  bind:visible-change="onWalletPopupVisibleChange"
  close-on-overlay-click="{{true}}"
>
  <view class="wallet-popup">
    <view class="wallet-popup__header">
      <view class="wallet-popup__title">钱包余额</view>
      <t-icon name="close" size="44rpx" color="#999999" bindtap="closeWalletPopup" />
    </view>

    <view class="wallet-popup__content">
      <view class="wallet-balance-display">
        <t-icon name="wallet" size="80rpx" color="#0052D9" />
        <view class="balance-amount">
          <text class="currency-symbol">¥</text>
          <text class="balance-value">{{isLoad ? personalInfo.balance || '0.00' : '0.00'}}</text>
        </view>
        <view class="balance-label">当前余额</view>
      </view>
    </view>
  </view>
</t-popup>
<login-overlay
  visible="{{loginOverlayVisible}}"
  bind:overlayclick="onLoginOverlayClick"
  bindcustomevent="handleLoginSuccess"
/>